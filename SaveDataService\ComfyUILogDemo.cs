using System;
using System.Threading.Tasks;
using SaveDataService.Manage;
using Newtonsoft.Json;
using System.Net.Http;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI详细日志演示程序
    /// </summary>
    public static class ComfyUILogDemo
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        /// <summary>
        /// 运行详细日志演示
        /// </summary>
        public static async Task RunLogDemo()
        {
            var logger = ComfyUILogger.Instance;
            var monitor = ComfyUIMonitor.Instance;

            Console.WriteLine("🎯 ComfyUI详细日志演示开始");
            Console.WriteLine("这个演示将展示ComfyUI工作流执行的每个环节的详细日志记录");
            Console.WriteLine();

            // 创建一个真实的工作流
            var workflowJson = CreateDemoWorkflow();
            var taskId = Guid.NewGuid().ToString();
            var serverUrl = "http://127.0.0.1:8888";

            try
            {
                // 1. 记录工作流开始
                logger.LogWorkflowStart(taskId, workflowJson, "demo-server");

                // 2. 提交工作流到真实的ComfyUI服务器
                var url = $"{serverUrl}/prompt";
                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                logger.LogInfo("向ComfyUI服务器提交工作流...");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                // 3. 记录提交结果
                logger.LogWorkflowSubmission(taskId, response.IsSuccessStatusCode, responseText);

                if (response.IsSuccessStatusCode)
                {
                    // 4. 解析响应获取prompt_id
                    var responseObj = JsonConvert.DeserializeObject<dynamic>(responseText);
                    if (responseObj?.prompt_id != null)
                    {
                        var promptId = responseObj.prompt_id.ToString();
                        logger.LogInfo($"获得Prompt ID: {promptId}，开始详细监控...");

                        // 5. 启动详细监控
                        await monitor.StartMonitoring(taskId, promptId, serverUrl, workflowJson);

                        logger.LogInfo("监控已启动，工作流正在执行中...");
                        logger.LogInfo("请观察上面的详细执行日志！");

                        // 6. 等待一段时间让工作流完成
                        await Task.Delay(45000); // 等待45秒

                        // 7. 显示最终统计
                        var activeMonitors = monitor.GetActiveMonitorCount();
                        logger.LogInfo($"当前活动监控数量: {activeMonitors}");
                    }
                    else
                    {
                        logger.LogError("无法从响应中获取prompt_id");
                    }
                }
                else
                {
                    logger.LogError($"工作流提交失败: {response.StatusCode}");
                    logger.LogError($"错误详情: {responseText}");
                }
            }
            catch (Exception ex)
            {
                logger.LogError($"演示过程中发生错误: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("🏁 ComfyUI详细日志演示结束");
            Console.WriteLine();
            Console.WriteLine("📋 日志说明:");
            Console.WriteLine("🚀 = 工作流开始");
            Console.WriteLine("📤 = 工作流提交");
            Console.WriteLine("🔄 = 节点开始执行");
            Console.WriteLine("⏳ = 节点执行进度");
            Console.WriteLine("✅ = 节点执行完成");
            Console.WriteLine("❌ = 节点执行失败");
            Console.WriteLine("📋 = 队列状态");
            Console.WriteLine("📚 = 历史查询");
            Console.WriteLine("🎉 = 工作流完成");
            Console.WriteLine("ℹ️ = 一般信息");
            Console.WriteLine("⚠️ = 警告信息");
            Console.WriteLine("❌ = 错误信息");
        }

        /// <summary>
        /// 创建演示用的工作流
        /// </summary>
        private static string CreateDemoWorkflow()
        {
            var workflow = new Dictionary<string, object>
            {
                // 检查点加载器
                ["4"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CheckpointLoaderSimple",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["ckpt_name"] = "v1-5-pruned-emaonly-fp16.safetensors"
                    }
                },
                // 空白潜在图像
                ["5"] = new Dictionary<string, object>
                {
                    ["class_type"] = "EmptyLatentImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["width"] = 512,
                        ["height"] = 512,
                        ["batch_size"] = 1
                    }
                },
                // 正面提示词编码
                ["6"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "a beautiful sunset over mountains, detailed landscape, high quality"
                    }
                },
                // 负面提示词编码
                ["7"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "blurry, low quality, bad anatomy"
                    }
                },
                // KSampler采样器
                ["3"] = new Dictionary<string, object>
                {
                    ["class_type"] = "KSampler",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["model"] = new object[] { "4", 0 },
                        ["positive"] = new object[] { "6", 0 },
                        ["negative"] = new object[] { "7", 0 },
                        ["latent_image"] = new object[] { "5", 0 },
                        ["seed"] = 42,
                        ["steps"] = 20,
                        ["cfg"] = 7.0,
                        ["sampler_name"] = "euler",
                        ["scheduler"] = "normal",
                        ["denoise"] = 1.0
                    }
                },
                // VAE解码器
                ["8"] = new Dictionary<string, object>
                {
                    ["class_type"] = "VAEDecode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["samples"] = new object[] { "3", 0 },
                        ["vae"] = new object[] { "4", 2 }
                    }
                },
                // 保存图像
                ["9"] = new Dictionary<string, object>
                {
                    ["class_type"] = "SaveImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["images"] = new object[] { "8", 0 },
                        ["filename_prefix"] = "ComfyUI_LogDemo"
                    }
                }
            };

            return JsonConvert.SerializeObject(workflow, Formatting.Indented);
        }

        /// <summary>
        /// 运行模拟日志演示（不需要真实的ComfyUI服务器）
        /// </summary>
        public static async Task RunMockLogDemo()
        {
            var logger = ComfyUILogger.Instance;

            Console.WriteLine("🎯 ComfyUI模拟日志演示开始");
            Console.WriteLine("这个演示将模拟ComfyUI工作流执行的完整过程");
            Console.WriteLine();

            var taskId = Guid.NewGuid().ToString();
            var workflowJson = CreateDemoWorkflow();

            // 1. 工作流开始
            logger.LogWorkflowStart(taskId, workflowJson, "mock-server");

            // 2. 模拟提交成功
            var mockResponse = $"{{\"prompt_id\": \"{Guid.NewGuid()}\", \"number\": 1}}";
            logger.LogWorkflowSubmission(taskId, true, mockResponse);

            // 3. 模拟节点执行过程
            var nodes = new[]
            {
                ("4", "CheckpointLoaderSimple", "加载Stable Diffusion模型"),
                ("5", "EmptyLatentImage", "创建512x512空白潜在图像"),
                ("6", "CLIPTextEncode", "编码正面提示词"),
                ("7", "CLIPTextEncode", "编码负面提示词"),
                ("3", "KSampler", "执行扩散采样生成"),
                ("8", "VAEDecode", "VAE解码为最终图像"),
                ("9", "SaveImage", "保存生成的图像")
            };

            foreach (var (nodeId, nodeType, description) in nodes)
            {
                // 节点开始
                var inputs = new Newtonsoft.Json.Linq.JObject();
                inputs["description"] = description;
                logger.LogNodeStart(taskId, nodeId, nodeType, nodeType, inputs);

                // 模拟执行时间和进度
                for (int progress = 0; progress <= 100; progress += 25)
                {
                    await Task.Delay(500);
                    if (progress < 100)
                    {
                        logger.LogNodeProgress(taskId, nodeId, nodeType, progress, "执行中");
                    }
                }

                // 节点完成
                var outputs = new Newtonsoft.Json.Linq.JObject();
                outputs["result"] = $"{nodeType} 执行完成";
                if (nodeType == "SaveImage")
                {
                    outputs["filename"] = "ComfyUI_LogDemo_00001_.png";
                }
                logger.LogNodeComplete(taskId, nodeId, nodeType, outputs, TimeSpan.FromSeconds(2));
            }

            // 4. 工作流完成
            var outputFiles = new[] { "ComfyUI_LogDemo_00001_.png" };
            logger.LogWorkflowComplete(taskId, true, TimeSpan.FromSeconds(14), outputFiles.ToList());

            Console.WriteLine();
            Console.WriteLine("🏁 ComfyUI模拟日志演示结束");
        }
    }
}
