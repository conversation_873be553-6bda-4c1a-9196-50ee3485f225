using System;
using System.Threading.Tasks;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI日志演示运行器
    /// </summary>
    class ComfyUILogDemoRunner
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🎯 ComfyUI详细日志功能演示");
            Console.WriteLine("这个演示展示了当ComfyUI服务器工作流跑起来之后，");
            Console.WriteLine("系统如何通过详细日志打印出执行的每个环节，");
            Console.WriteLine("包含工作流的每个节点的名字、输入输出等等。");
            Console.WriteLine();
            Console.WriteLine("按任意键开始演示...");
            Console.ReadKey();
            Console.WriteLine();

            await ComfyUILogDemoStandalone.RunStandaloneDemo();

            Console.WriteLine();
            Console.WriteLine("演示完成！");
            Console.WriteLine("这就是ComfyUI详细日志系统的完整功能展示。");
            Console.WriteLine();
            Console.WriteLine("主要特性包括:");
            Console.WriteLine("✅ 工作流开始/结束的完整记录");
            Console.WriteLine("✅ 每个节点的详细执行信息");
            Console.WriteLine("✅ 节点输入参数的完整显示");
            Console.WriteLine("✅ 实时执行进度监控");
            Console.WriteLine("✅ 节点输出结果记录");
            Console.WriteLine("✅ 执行时间统计");
            Console.WriteLine("✅ 输出文件列表");
            Console.WriteLine("✅ 丰富的emoji图标标识");
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
