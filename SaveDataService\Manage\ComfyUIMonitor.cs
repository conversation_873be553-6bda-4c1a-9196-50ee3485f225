using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI工作流执行监控器
    /// </summary>
    public class ComfyUIMonitor
    {
        private static ComfyUIMonitor? _instance;
        public static ComfyUIMonitor Instance => _instance ??= new ComfyUIMonitor();

        private readonly HttpClient _httpClient;
        private readonly Dictionary<string, WorkflowMonitorInfo> _activeMonitors;
        private readonly ComfyUILogger _logger;

        private ComfyUIMonitor()
        {
            _httpClient = new HttpClient();
            _activeMonitors = new Dictionary<string, WorkflowMonitorInfo>();
            _logger = ComfyUILogger.Instance;
        }

        /// <summary>
        /// 开始监控工作流执行
        /// </summary>
        public async Task StartMonitoring(string taskId, string promptId, string serverUrl, string workflowJson)
        {
            var monitorInfo = new WorkflowMonitorInfo
            {
                TaskId = taskId,
                PromptId = promptId,
                ServerUrl = serverUrl,
                WorkflowJson = workflowJson,
                StartTime = DateTime.Now,
                IsActive = true
            };

            _activeMonitors[promptId] = monitorInfo;

            _logger.LogInfo($"开始监控工作流执行 [任务: {taskId}] [Prompt: {promptId}]");

            // 解析工作流节点信息
            try
            {
                var workflow = JsonConvert.DeserializeObject<JObject>(workflowJson);
                if (workflow != null)
                {
                    monitorInfo.Nodes = new Dictionary<string, NodeInfo>();
                    foreach (var node in workflow)
                    {
                        var nodeData = node.Value as JObject;
                        var nodeInfo = new NodeInfo
                        {
                            NodeId = node.Key,
                            ClassType = nodeData?["class_type"]?.ToString() ?? "Unknown",
                            Inputs = nodeData?["inputs"] as JObject,
                            Status = "Pending"
                        };
                        monitorInfo.Nodes[node.Key] = nodeInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"解析工作流节点失败: {ex.Message}");
            }

            // 启动监控任务
            _ = Task.Run(() => MonitorWorkflowExecution(monitorInfo));
        }

        /// <summary>
        /// 监控工作流执行过程
        /// </summary>
        private async Task MonitorWorkflowExecution(WorkflowMonitorInfo monitorInfo)
        {
            var lastStatus = "";
            var nodeStartTimes = new Dictionary<string, DateTime>();

            try
            {
                while (monitorInfo.IsActive)
                {
                    // 查询队列状态
                    await CheckQueueStatus(monitorInfo);

                    // 查询历史状态
                    var historyData = await GetHistoryData(monitorInfo);
                    if (historyData != null)
                    {
                        var currentStatus = historyData["status"]?.ToString() ?? "";
                        
                        // 状态变化时记录日志
                        if (currentStatus != lastStatus)
                        {
                            _logger.LogInfo($"工作流状态变化: {lastStatus} -> {currentStatus} [任务: {monitorInfo.TaskId}]");
                            lastStatus = currentStatus;
                        }

                        // 检查节点执行状态
                        await CheckNodeExecution(monitorInfo, historyData, nodeStartTimes);

                        // 检查是否完成
                        if (currentStatus == "success" || currentStatus == "error" || 
                            historyData["outputs"] != null)
                        {
                            await HandleWorkflowCompletion(monitorInfo, historyData);
                            break;
                        }
                    }

                    // 等待一段时间再次检查
                    await Task.Delay(2000);

                    // 超时检查 (10分钟)
                    if (DateTime.Now - monitorInfo.StartTime > TimeSpan.FromMinutes(10))
                    {
                        _logger.LogWarning($"工作流监控超时 [任务: {monitorInfo.TaskId}]");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"监控工作流执行时发生错误: {ex.Message}");
            }
            finally
            {
                monitorInfo.IsActive = false;
                _activeMonitors.Remove(monitorInfo.PromptId);
            }
        }

        /// <summary>
        /// 检查队列状态
        /// </summary>
        private async Task CheckQueueStatus(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/queue";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var queueData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    var running = queueData?["queue_running"] as JArray;
                    var pending = queueData?["queue_pending"] as JArray;
                    
                    var totalInQueue = (pending?.Count ?? 0) + (running?.Count ?? 0);
                    var position = FindQueuePosition(monitorInfo.PromptId, running, pending);
                    
                    if (position >= 0)
                    {
                        _logger.LogQueueStatus(monitorInfo.TaskId, position, totalInQueue);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询队列状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取历史数据
        /// </summary>
        private async Task<JObject?> GetHistoryData(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/history/{monitorInfo.PromptId}";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var historyData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    if (historyData?[monitorInfo.PromptId] != null)
                    {
                        return historyData[monitorInfo.PromptId] as JObject;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询历史数据失败: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 检查节点执行状态
        /// </summary>
        private async Task CheckNodeExecution(WorkflowMonitorInfo monitorInfo, JObject historyData, Dictionary<string, DateTime> nodeStartTimes)
        {
            try
            {
                var outputs = historyData["outputs"] as JObject;
                if (outputs != null && monitorInfo.Nodes != null)
                {
                    foreach (var output in outputs)
                    {
                        var nodeId = output.Key;
                        if (monitorInfo.Nodes.ContainsKey(nodeId))
                        {
                            var nodeInfo = monitorInfo.Nodes[nodeId];
                            
                            // 如果节点还没开始，记录开始
                            if (nodeInfo.Status == "Pending")
                            {
                                nodeInfo.Status = "Running";
                                nodeStartTimes[nodeId] = DateTime.Now;
                                _logger.LogNodeStart(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, nodeInfo.ClassType, nodeInfo.Inputs);
                            }
                            
                            // 如果节点正在运行，检查是否完成
                            if (nodeInfo.Status == "Running" && output.Value != null)
                            {
                                nodeInfo.Status = "Completed";
                                var duration = DateTime.Now - (nodeStartTimes.ContainsKey(nodeId) ? nodeStartTimes[nodeId] : DateTime.Now);
                                _logger.LogNodeComplete(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, output.Value as JObject, duration);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"检查节点执行状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理工作流完成
        /// </summary>
        private async Task HandleWorkflowCompletion(WorkflowMonitorInfo monitorInfo, JObject historyData)
        {
            var success = historyData["status"]?.ToString() == "success" || historyData["outputs"] != null;
            var duration = DateTime.Now - monitorInfo.StartTime;
            var outputFiles = new List<string>();

            // 收集输出文件
            try
            {
                var outputs = historyData["outputs"] as JObject;
                if (outputs != null)
                {
                    foreach (var output in outputs)
                    {
                        var outputData = output.Value as JObject;
                        if (outputData != null)
                        {
                            foreach (var item in outputData)
                            {
                                if (item.Value is JArray array)
                                {
                                    foreach (var file in array)
                                    {
                                        if (file is JObject fileObj && fileObj["filename"] != null)
                                        {
                                            outputFiles.Add(fileObj["filename"].ToString());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"收集输出文件失败: {ex.Message}");
            }

            _logger.LogWorkflowComplete(monitorInfo.TaskId, success, duration, outputFiles);
        }

        /// <summary>
        /// 查找队列位置
        /// </summary>
        private int FindQueuePosition(string promptId, JArray? running, JArray? pending)
        {
            // 检查运行中的队列
            if (running != null)
            {
                for (int i = 0; i < running.Count; i++)
                {
                    if (running[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return 0; // 正在运行
                    }
                }
            }

            // 检查等待中的队列
            if (pending != null)
            {
                for (int i = 0; i < pending.Count; i++)
                {
                    if (pending[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return i + 1; // 等待位置
                    }
                }
            }

            return -1; // 未找到
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring(string promptId)
        {
            if (_activeMonitors.ContainsKey(promptId))
            {
                _activeMonitors[promptId].IsActive = false;
                _activeMonitors.Remove(promptId);
                _logger.LogInfo($"停止监控工作流 [Prompt: {promptId}]");
            }
        }

        /// <summary>
        /// 获取活动监控数量
        /// </summary>
        public int GetActiveMonitorCount()
        {
            return _activeMonitors.Count;
        }
    }

    /// <summary>
    /// 工作流监控信息
    /// </summary>
    public class WorkflowMonitorInfo
    {
        public string TaskId { get; set; } = "";
        public string PromptId { get; set; } = "";
        public string ServerUrl { get; set; } = "";
        public string WorkflowJson { get; set; } = "";
        public DateTime StartTime { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, NodeInfo>? Nodes { get; set; }
    }

    /// <summary>
    /// 节点信息
    /// </summary>
    public class NodeInfo
    {
        public string NodeId { get; set; } = "";
        public string ClassType { get; set; } = "";
        public JObject? Inputs { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Running, Completed, Error
    }
}
