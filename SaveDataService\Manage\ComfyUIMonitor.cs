using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.IO;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SaveDataService.Manage
{
    /// <summary>
    /// ComfyUI工作流执行监控器
    /// </summary>
    public class ComfyUIMonitor
    {
        private static ComfyUIMonitor? _instance;
        public static ComfyUIMonitor Instance => _instance ??= new ComfyUIMonitor();

        private readonly HttpClient _httpClient;
        private readonly Dictionary<string, WorkflowMonitorInfo> _activeMonitors;
        private readonly ComfyUILogger _logger;

        private ComfyUIMonitor()
        {
            _httpClient = new HttpClient();
            _activeMonitors = new Dictionary<string, WorkflowMonitorInfo>();
            _logger = ComfyUILogger.Instance;
        }

        /// <summary>
        /// 开始监控工作流执行
        /// </summary>
        public async Task StartMonitoring(string taskId, string promptId, string serverUrl, string workflowJson)
        {
            var monitorInfo = new WorkflowMonitorInfo
            {
                TaskId = taskId,
                PromptId = promptId,
                ServerUrl = serverUrl,
                WorkflowJson = workflowJson,
                StartTime = DateTime.Now,
                IsActive = true
            };

            _activeMonitors[promptId] = monitorInfo;

            _logger.LogInfo($"开始监控工作流执行 [任务: {taskId}] [Prompt: {promptId}]");

            // 解析工作流节点信息
            try
            {
                var workflow = JsonConvert.DeserializeObject<JObject>(workflowJson);
                if (workflow != null)
                {
                    monitorInfo.Nodes = new Dictionary<string, NodeInfo>();
                    foreach (var node in workflow)
                    {
                        var nodeData = node.Value as JObject;
                        var nodeInfo = new NodeInfo
                        {
                            NodeId = node.Key,
                            ClassType = nodeData?["class_type"]?.ToString() ?? "Unknown",
                            Inputs = nodeData?["inputs"] as JObject,
                            Status = "Pending"
                        };
                        monitorInfo.Nodes[node.Key] = nodeInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"解析工作流节点失败: {ex.Message}");
            }

            // 启动监控任务
            _ = Task.Run(() => MonitorWorkflowExecution(monitorInfo));
        }

        /// <summary>
        /// 监控工作流执行过程
        /// </summary>
        private async Task MonitorWorkflowExecution(WorkflowMonitorInfo monitorInfo)
        {
            var lastStatus = "";
            var nodeStartTimes = new Dictionary<string, DateTime>();
            var workflowCompleted = false;

            try
            {
                while (monitorInfo.IsActive && !workflowCompleted)
                {
                    // 查询队列状态
                    await CheckQueueStatus(monitorInfo);

                    // 查询历史状态
                    var historyData = await GetHistoryData(monitorInfo);
                    if (historyData != null)
                    {
                        var currentStatus = historyData["status"]?.ToString() ?? "";

                        // 状态变化时记录日志
                        if (currentStatus != lastStatus && !string.IsNullOrEmpty(currentStatus))
                        {
                            _logger.LogInfo($"工作流状态变化: {lastStatus} -> {historyData} [任务: {monitorInfo.TaskId}]");
                            lastStatus = currentStatus;
                        }

                        // 检查节点执行状态
                        await CheckNodeExecution(monitorInfo, historyData, nodeStartTimes);

                        // 检查是否完成
                        if (currentStatus == "success" || currentStatus == "error" ||
                            historyData["outputs"] != null)
                        {
                            await HandleWorkflowCompletion(monitorInfo, historyData);
                            workflowCompleted = true;
                            break;
                        }
                    }

                    // 等待一段时间再次检查
                    await Task.Delay(2000);

                    // 超时检查 (10分钟)
                    if (DateTime.Now - monitorInfo.StartTime > TimeSpan.FromMinutes(10))
                    {
                        _logger.LogWarning($"工作流监控超时 [任务: {monitorInfo.TaskId}]");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"监控工作流执行时发生错误: {ex.Message}");
            }
            finally
            {
                monitorInfo.IsActive = false;
                _activeMonitors.Remove(monitorInfo.PromptId);
                _logger.LogInfo($"工作流监控结束 [任务: {monitorInfo.TaskId}] [Prompt: {monitorInfo.PromptId}]");
            }
        }

        /// <summary>
        /// 检查队列状态
        /// </summary>
        private async Task CheckQueueStatus(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/queue";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var queueData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    var running = queueData?["queue_running"] as JArray;
                    var pending = queueData?["queue_pending"] as JArray;
                    
                    var totalInQueue = (pending?.Count ?? 0) + (running?.Count ?? 0);
                    var position = FindQueuePosition(monitorInfo.PromptId, running, pending);
                    
                    if (position >= 0)
                    {
                        _logger.LogQueueStatus(monitorInfo.TaskId, position, totalInQueue);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询队列状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取历史数据
        /// </summary>
        private async Task<JObject?> GetHistoryData(WorkflowMonitorInfo monitorInfo)
        {
            try
            {
                var url = $"{monitorInfo.ServerUrl}/history/{monitorInfo.PromptId}";
                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var historyData = JsonConvert.DeserializeObject<JObject>(content);
                    
                    if (historyData?[monitorInfo.PromptId] != null)
                    {
                        return historyData[monitorInfo.PromptId] as JObject;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"查询历史数据失败: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 检查节点执行状态
        /// </summary>
        private async Task CheckNodeExecution(WorkflowMonitorInfo monitorInfo, JObject historyData, Dictionary<string, DateTime> nodeStartTimes)
        {
            try
            {
                var outputs = historyData["outputs"] as JObject;
                if (outputs != null && monitorInfo.Nodes != null)
                {
                    foreach (var output in outputs)
                    {
                        var nodeId = output.Key;
                        if (monitorInfo.Nodes.ContainsKey(nodeId))
                        {
                            var nodeInfo = monitorInfo.Nodes[nodeId];
                            
                            // 如果节点还没开始，记录开始
                            if (nodeInfo.Status == "Pending")
                            {
                                nodeInfo.Status = "Running";
                                nodeStartTimes[nodeId] = DateTime.Now;
                                _logger.LogNodeStart(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, nodeInfo.ClassType, nodeInfo.Inputs);
                            }
                            
                            // 如果节点正在运行，检查是否完成
                            if (nodeInfo.Status == "Running" && output.Value != null)
                            {
                                nodeInfo.Status = "Completed";
                                var duration = DateTime.Now - (nodeStartTimes.ContainsKey(nodeId) ? nodeStartTimes[nodeId] : DateTime.Now);
                                _logger.LogNodeComplete(monitorInfo.TaskId, nodeId, nodeInfo.ClassType, output.Value as JObject, duration);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"检查节点执行状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理工作流完成
        /// </summary>
        private async Task HandleWorkflowCompletion(WorkflowMonitorInfo monitorInfo, JObject historyData)
        {
            var success = historyData["status"]?.ToString() == "success" || historyData["outputs"] != null;
            var duration = DateTime.Now - monitorInfo.StartTime;
            var outputFiles = new List<string>();
            var downloadedFiles = new List<string>();

            _logger.LogInfo($"开始处理工作流完成 [任务: {monitorInfo.TaskId}]");
            _logger.LogInfo($"历史数据结构: {historyData}");

            // 收集输出文件
            try
            {
                var outputs = historyData["outputs"] as JObject;
                _logger.LogInfo($"输出数据: {outputs}");

                if (outputs != null)
                {
                    _logger.LogInfo($"找到输出数据，节点数量: {outputs.Count}");

                    foreach (var output in outputs)
                    {
                        _logger.LogInfo($"处理输出节点: {output.Key}");
                        var outputData = output.Value as JObject;
                        if (outputData != null)
                        {
                            _logger.LogInfo($"节点 {output.Key} 输出数据: {outputData}");

                            foreach (var item in outputData)
                            {
                                _logger.LogInfo($"处理输出项: {item.Key} = {item.Value}");

                                if (item.Value is JArray array)
                                {
                                    _logger.LogInfo($"找到文件数组，文件数量: {array.Count}");

                                    foreach (var file in array)
                                    {
                                        _logger.LogInfo($"处理文件: {file}");

                                        if (file is JObject fileObj && fileObj["filename"] != null)
                                        {
                                            var filename = fileObj["filename"].ToString();
                                            outputFiles.Add(filename);
                                            _logger.LogInfo($"找到输出文件: {filename}");

                                            // 下载文件到本地
                                            var downloadedPath = await DownloadOutputFile(monitorInfo, filename, fileObj);
                                            if (!string.IsNullOrEmpty(downloadedPath))
                                            {
                                                downloadedFiles.Add(downloadedPath);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("没有找到输出数据");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"收集输出文件失败: {ex.Message}");
                _logger.LogError($"异常堆栈: {ex.StackTrace}");
            }

            _logger.LogWorkflowComplete(monitorInfo.TaskId, success, duration, outputFiles);

            // 记录批量下载结果
            if (outputFiles.Count > 0)
            {
                var successCount = downloadedFiles.Count;
                var failedCount = outputFiles.Count - successCount;
                _logger.LogBatchDownloadComplete(monitorInfo.TaskId, outputFiles.Count, successCount, failedCount, downloadedFiles);
            }
            else
            {
                _logger.LogWarning($"没有找到任何输出文件 [任务: {monitorInfo.TaskId}]");
            }
        }

        /// <summary>
        /// 下载输出文件到本地
        /// </summary>
        private async Task<string> DownloadOutputFile(WorkflowMonitorInfo monitorInfo, string filename, JObject fileObj)
        {
            var startTime = DateTime.Now;
            var config = ComfyUIDownloadConfig.Instance;

            _logger.LogInfo($"开始下载文件: {filename}");
            _logger.LogInfo($"文件对象: {fileObj}");

            try
            {
                // 确定文件类型和扩展名
                var fileExtension = Path.GetExtension(filename).ToLower();
                var fileType = GetFileType(fileExtension);

                _logger.LogInfo($"文件扩展名: {fileExtension}, 文件类型: {fileType}");

                // 检查文件类型是否被支持
                if (!config.IsFileTypeSupported(fileExtension))
                {
                    _logger.LogInfo($"跳过不支持的文件类型: {filename} ({fileType})");
                    return "";
                }

                // 创建本地下载目录
                var downloadDir = config.GetDownloadPath(monitorInfo.TaskId, fileType, filename);
                Directory.CreateDirectory(downloadDir);
                _logger.LogInfo($"下载目录: {downloadDir}");

                // 构建下载URL
                var downloadUrl = $"{monitorInfo.ServerUrl}/view";

                // 从fileObj中获取文件信息
                var subfolder = fileObj["subfolder"]?.ToString() ?? "";
                var type = fileObj["type"]?.ToString() ?? "output";

                _logger.LogInfo($"文件信息 - subfolder: '{subfolder}', type: '{type}'");

                // 构建完整的下载URL
                var fullUrl = $"{downloadUrl}?filename={Uri.EscapeDataString(filename)}&subfolder={Uri.EscapeDataString(subfolder)}&type={type}";

                // 记录下载开始
                _logger.LogFileDownloadStart(monitorInfo.TaskId, filename, fileType, fullUrl);

                // 下载文件
                using var response = await _httpClient.GetAsync(fullUrl);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsByteArrayAsync();

                    // 计算文件大小
                    var fileSizeMB = content.Length / (1024.0 * 1024.0);

                    // 检查文件大小限制
                    if (!config.IsFileSizeAllowed(content.Length))
                    {
                        _logger.LogFileDownloadError(monitorInfo.TaskId, filename, $"文件大小 {fileSizeMB:F2} MB 超过限制 {config.MaxFileSizeMB} MB");
                        return "";
                    }

                    // 生成最终文件名
                    var finalFilename = config.GenerateFilename(filename);
                    var localFilePath = Path.Combine(downloadDir, finalFilename);

                    // 写入文件
                    await File.WriteAllBytesAsync(localFilePath, content);

                    var duration = DateTime.Now - startTime;
                    var fullPath = Path.GetFullPath(localFilePath);

                    // 记录下载成功
                    _logger.LogFileDownloadSuccess(monitorInfo.TaskId, filename, fullPath, fileSizeMB, duration);

                    return localFilePath;
                }
                else
                {
                    var error = $"HTTP {response.StatusCode}: {response.ReasonPhrase}";
                    _logger.LogFileDownloadError(monitorInfo.TaskId, filename, error);
                    return "";
                }
            }
            catch (Exception ex)
            {
                _logger.LogFileDownloadError(monitorInfo.TaskId, filename, ex.Message);
                return "";
            }
        }

        /// <summary>
        /// 根据文件扩展名确定文件类型
        /// </summary>
        private string GetFileType(string extension)
        {
            return extension.ToLower() switch
            {
                // 图片文件
                ".png" or ".jpg" or ".jpeg" or ".gif" or ".bmp" or ".tiff" or ".webp" or ".svg" or ".ico" or ".tga" or ".exr" or ".hdr" => "图片",

                // 视频文件
                ".mp4" or ".avi" or ".mov" or ".wmv" or ".flv" or ".mkv" or ".webm" or ".m4v" or ".3gp" or ".ogv" or ".ts" or ".mts" => "视频",

                // 3D模型文件
                ".fbx" or ".obj" or ".dae" or ".3ds" or ".blend" or ".max" or ".ma" or ".mb" or ".c4d" or ".lwo" or ".x3d" or ".ply" or ".stl" or ".gltf" or ".glb" => "3D模型",

                // 音频文件
                ".wav" or ".mp3" or ".flac" or ".aac" or ".ogg" or ".wma" or ".m4a" or ".opus" or ".aiff" or ".au" => "音频",

                // 文本文件
                ".txt" or ".json" or ".xml" or ".csv" or ".yaml" or ".yml" or ".ini" or ".cfg" or ".conf" or ".log" or ".md" or ".rst" => "文本",

                // 压缩包
                ".zip" or ".rar" or ".7z" or ".tar" or ".gz" or ".bz2" or ".xz" or ".lzma" or ".cab" or ".iso" => "压缩包",

                // 文档
                ".pdf" or ".doc" or ".docx" or ".xls" or ".xlsx" or ".ppt" or ".pptx" or ".odt" or ".ods" or ".odp" => "文档",

                // 可执行文件
                ".exe" or ".msi" or ".dmg" or ".pkg" or ".deb" or ".rpm" or ".app" or ".apk" => "可执行文件",

                // 字体文件
                ".ttf" or ".otf" or ".woff" or ".woff2" or ".eot" => "字体",

                // 数据文件
                ".db" or ".sqlite" or ".sql" or ".mdb" or ".accdb" => "数据库",

                // 脚本文件
                ".py" or ".js" or ".ts" or ".php" or ".rb" or ".pl" or ".sh" or ".bat" or ".ps1" => "脚本",

                // 其他常见格式
                ".psd" or ".ai" or ".eps" or ".sketch" => "设计文件",
                ".unity" or ".unitypackage" => "Unity资源",
                ".uasset" or ".umap" => "Unreal资源",

                _ => "未知类型"
            };
        }

        /// <summary>
        /// 查找队列位置
        /// </summary>
        private int FindQueuePosition(string promptId, JArray? running, JArray? pending)
        {
            // 检查运行中的队列
            if (running != null)
            {
                for (int i = 0; i < running.Count; i++)
                {
                    if (running[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return 0; // 正在运行
                    }
                }
            }

            // 检查等待中的队列
            if (pending != null)
            {
                for (int i = 0; i < pending.Count; i++)
                {
                    if (pending[i] is JArray item && item.Count > 1 && item[1]?.ToString() == promptId)
                    {
                        return i + 1; // 等待位置
                    }
                }
            }

            return -1; // 未找到
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public void StopMonitoring(string promptId)
        {
            if (_activeMonitors.ContainsKey(promptId))
            {
                _activeMonitors[promptId].IsActive = false;
                _activeMonitors.Remove(promptId);
                _logger.LogInfo($"停止监控工作流 [Prompt: {promptId}]");
            }
        }

        /// <summary>
        /// 获取活动监控数量
        /// </summary>
        public int GetActiveMonitorCount()
        {
            return _activeMonitors.Count;
        }
    }

    /// <summary>
    /// 工作流监控信息
    /// </summary>
    public class WorkflowMonitorInfo
    {
        public string TaskId { get; set; } = "";
        public string PromptId { get; set; } = "";
        public string ServerUrl { get; set; } = "";
        public string WorkflowJson { get; set; } = "";
        public DateTime StartTime { get; set; }
        public bool IsActive { get; set; }
        public Dictionary<string, NodeInfo>? Nodes { get; set; }
    }

    /// <summary>
    /// 节点信息
    /// </summary>
    public class NodeInfo
    {
        public string NodeId { get; set; } = "";
        public string ClassType { get; set; } = "";
        public JObject? Inputs { get; set; }
        public string Status { get; set; } = "Pending"; // Pending, Running, Completed, Error
    }
}
