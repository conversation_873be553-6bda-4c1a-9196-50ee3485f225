using System;
using System.IO;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI简单测试类 - 不依赖数据库
    /// </summary>
    public static class ComfyUISimpleTest
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        /// <summary>
        /// 运行简单测试
        /// </summary>
        public static async Task RunSimpleTests()
        {
            Console.WriteLine("=== ComfyUI简单测试开始 ===");

            try
            {
                // 1. 测试ComfyUI服务器连接
                Console.WriteLine("\n1. 测试ComfyUI服务器连接...");
                await TestComfyUIServerConnection();

                // 2. 测试加载工作流文件
                Console.WriteLine("\n2. 测试加载工作流文件...");
                var workflowJson = LoadWorkflowFile();
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    Console.WriteLine($"工作流加载成功，长度: {workflowJson.Length} 字符");
                }

                // 3. 测试解析工作流
                Console.WriteLine("\n3. 测试解析工作流...");
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    TestParseWorkflow(workflowJson);
                }

                // 4. 测试向ComfyUI服务器提交工作流
                Console.WriteLine("\n4. 测试向ComfyUI服务器提交工作流...");
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    await TestSubmitWorkflow(workflowJson);
                }

                // 4.1 测试简化工作流
                Console.WriteLine("\n4.1 测试简化工作流...");
                var simpleWorkflow = CreateSimpleTestWorkflow();
                await TestSubmitSimpleWorkflow(simpleWorkflow);

                // 5. 测试ComfyUI管理类的基本功能
                Console.WriteLine("\n5. 测试ComfyUI管理类基本功能...");
                TestComfyUIManageBasics();

                Console.WriteLine("\n=== ComfyUI简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试ComfyUI服务器连接
        /// </summary>
        private static async Task TestComfyUIServerConnection()
        {
            try
            {
                var url = "http://127.0.0.1:8888/";
                Console.WriteLine($"尝试连接到: {url}");

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✓ ComfyUI服务器连接成功");
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"响应长度: {content.Length} 字符");
                }
                else
                {
                    Console.WriteLine($"✗ ComfyUI服务器连接失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 连接ComfyUI服务器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载工作流文件
        /// </summary>
        private static string LoadWorkflowFile()
        {
            try
            {
                var workflowPath = Path.Combine("Res", "baseExcel", "comfyui", "comfyui-cafelabs-test.json");
                if (File.Exists(workflowPath))
                {
                    var workflowJson = File.ReadAllText(workflowPath);
                    Console.WriteLine($"✓ 成功加载工作流文件: {workflowPath}");
                    return workflowJson;
                }
                else
                {
                    Console.WriteLine($"✗ 工作流文件不存在: {workflowPath}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 加载工作流文件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 测试解析工作流
        /// </summary>
        private static void TestParseWorkflow(string workflowJson)
        {
            try
            {
                // 尝试解析JSON
                var workflowObj = JsonConvert.DeserializeObject(workflowJson);
                Console.WriteLine("✓ 工作流JSON解析成功");

                // 尝试使用ComfyUIData解析
                var workflowInfo = ComfyUIData.Instance.ParseWorkflow(workflowJson);
                Console.WriteLine($"✓ ComfyUIData解析成功，包含 {workflowInfo.Nodes.Count} 个节点");

                // 显示前几个节点信息
                for (int i = 0; i < Math.Min(3, workflowInfo.Nodes.Count); i++)
                {
                    var node = workflowInfo.Nodes[i];
                    Console.WriteLine($"  节点 {i + 1}: {node.NodeTitle} ({node.NodeType})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 解析工作流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试向ComfyUI服务器提交工作流
        /// </summary>
        private static async Task TestSubmitWorkflow(string workflowJson)
        {
            try
            {
                var url = "http://127.0.0.1:8888/prompt";

                // 解析并转换工作流格式
                var workflowObj = JsonConvert.DeserializeObject<dynamic>(workflowJson);
                var convertedWorkflow = ConvertWorkflowFormat(workflowObj);

                var requestData = new
                {
                    prompt = convertedWorkflow,
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                Console.WriteLine("向ComfyUI服务器提交工作流...");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✓ 工作流提交成功");
                    Console.WriteLine($"响应: {responseText}");
                }
                else
                {
                    Console.WriteLine($"✗ 工作流提交失败: {response.StatusCode}");
                    Console.WriteLine($"错误响应: {responseText}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 提交工作流时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 转换工作流格式，将ComfyUI UI格式转换为API格式
        /// </summary>
        private static object ConvertWorkflowFormat(dynamic workflowObj)
        {
            try
            {
                var apiWorkflow = new Dictionary<string, object>();

                if (workflowObj.nodes != null)
                {
                    foreach (var node in workflowObj.nodes)
                    {
                        var nodeId = node.id.ToString();
                        var nodeData = new Dictionary<string, object>
                        {
                            ["class_type"] = node.type.ToString(),
                            ["inputs"] = new Dictionary<string, object>()
                        };

                        // 处理输入连接
                        var inputs = (Dictionary<string, object>)nodeData["inputs"];

                        if (node.inputs != null)
                        {
                            foreach (var input in node.inputs)
                            {
                                var inputName = input.name.ToString();

                                // 如果有连接，使用连接
                                if (input.link != null && input.link.ToString() != "null")
                                {
                                    // 查找连接的源节点
                                    var linkId = int.Parse(input.link.ToString());
                                    var sourceInfo = FindSourceNode(workflowObj, linkId);
                                    if (sourceInfo != null)
                                    {
                                        inputs[inputName] = new object[] { sourceInfo.nodeId, sourceInfo.outputIndex };
                                    }
                                }
                            }
                        }

                        // 处理widget值
                        if (node.widgets_values != null)
                        {
                            var widgetIndex = 0;
                            if (node.inputs != null)
                            {
                                foreach (var input in node.inputs)
                                {
                                    if (input.widget != null && (input.link == null || input.link.ToString() == "null"))
                                    {
                                        var inputName = input.name.ToString();
                                        if (widgetIndex < ((Newtonsoft.Json.Linq.JArray)node.widgets_values).Count)
                                        {
                                            inputs[inputName] = ((Newtonsoft.Json.Linq.JArray)node.widgets_values)[widgetIndex];
                                            widgetIndex++;
                                        }
                                    }
                                }
                            }
                        }

                        apiWorkflow[nodeId] = nodeData;
                    }
                }

                return apiWorkflow;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"转换工作流格式时发生错误: {ex.Message}");
                // 如果转换失败，返回原始格式
                return workflowObj;
            }
        }

        /// <summary>
        /// 查找连接的源节点
        /// </summary>
        private static (int nodeId, int outputIndex)? FindSourceNode(dynamic workflowObj, int linkId)
        {
            try
            {
                if (workflowObj.links != null)
                {
                    foreach (var link in workflowObj.links)
                    {
                        if (((Newtonsoft.Json.Linq.JArray)link).Count >= 6)
                        {
                            var currentLinkId = (int)((Newtonsoft.Json.Linq.JArray)link)[0];
                            if (currentLinkId == linkId)
                            {
                                var sourceNodeId = (int)((Newtonsoft.Json.Linq.JArray)link)[1];
                                var outputIndex = (int)((Newtonsoft.Json.Linq.JArray)link)[2];
                                return (sourceNodeId, outputIndex);
                            }
                        }
                    }
                }
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 创建一个简化的测试工作流
        /// </summary>
        private static string CreateSimpleTestWorkflow()
        {
            var workflow = new Dictionary<string, object>
            {
                ["3"] = new Dictionary<string, object>
                {
                    ["class_type"] = "KSampler",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["model"] = new object[] { "4", 0 },
                        ["positive"] = new object[] { "6", 0 },
                        ["negative"] = new object[] { "7", 0 },
                        ["latent_image"] = new object[] { "5", 0 },
                        ["seed"] = 665579281476891,
                        ["steps"] = 20,
                        ["cfg"] = 8.0,
                        ["sampler_name"] = "euler",
                        ["scheduler"] = "normal",
                        ["denoise"] = 1.0
                    }
                },
                ["4"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CheckpointLoaderSimple",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["ckpt_name"] = "v1-5-pruned-emaonly-fp16.safetensors"
                    }
                },
                ["5"] = new Dictionary<string, object>
                {
                    ["class_type"] = "EmptyLatentImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["width"] = 512,
                        ["height"] = 512,
                        ["batch_size"] = 1
                    }
                },
                ["6"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "beautiful scenery nature glass bottle landscape, purple galaxy bottle"
                    }
                },
                ["7"] = new Dictionary<string, object>
                {
                    ["class_type"] = "CLIPTextEncode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["clip"] = new object[] { "4", 1 },
                        ["text"] = "text, watermark"
                    }
                },
                ["8"] = new Dictionary<string, object>
                {
                    ["class_type"] = "VAEDecode",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["samples"] = new object[] { "3", 0 },
                        ["vae"] = new object[] { "4", 2 }
                    }
                },
                ["10"] = new Dictionary<string, object>
                {
                    ["class_type"] = "PreviewImage",
                    ["inputs"] = new Dictionary<string, object>
                    {
                        ["images"] = new object[] { "8", 0 }
                    }
                }
            };

            return JsonConvert.SerializeObject(workflow);
        }

        /// <summary>
        /// 测试提交简化工作流
        /// </summary>
        private static async Task TestSubmitSimpleWorkflow(string workflowJson)
        {
            try
            {
                var url = "http://127.0.0.1:8888/prompt";

                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                Console.WriteLine("向ComfyUI服务器提交简化工作流...");

                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✓ 简化工作流提交成功");
                    Console.WriteLine($"响应: {responseText}");

                    // 解析响应获取prompt_id
                    try
                    {
                        var responseObj = JsonConvert.DeserializeObject<dynamic>(responseText);
                        if (responseObj.prompt_id != null)
                        {
                            var promptId = responseObj.prompt_id.ToString();
                            Console.WriteLine($"任务ID: {promptId}");

                            // 等待一段时间后检查任务状态
                            await Task.Delay(3000);
                            await CheckTaskStatus(promptId);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析响应失败: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 简化工作流提交失败: {response.StatusCode}");
                    Console.WriteLine($"错误响应: {responseText}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 提交简化工作流时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查任务状态
        /// </summary>
        private static async Task CheckTaskStatus(string promptId)
        {
            try
            {
                var url = $"http://127.0.0.1:8888/history/{promptId}";
                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var responseText = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"任务状态查询成功: {responseText.Length} 字符");

                    // 简单解析状态
                    if (responseText.Contains("\"status\": \"success\"") || responseText.Contains("outputs"))
                    {
                        Console.WriteLine("✓ 任务执行成功");
                    }
                    else if (responseText.Contains("\"status\": \"error\""))
                    {
                        Console.WriteLine("✗ 任务执行失败");
                    }
                    else
                    {
                        Console.WriteLine("? 任务状态未知");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 查询任务状态失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 查询任务状态时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试ComfyUI管理类基本功能
        /// </summary>
        private static void TestComfyUIManageBasics()
        {
            try
            {
                Console.WriteLine("测试ComfyUIManage单例模式...");
                var manage1 = ComfyUIManage.Instance;
                var manage2 = ComfyUIManage.Instance;
                
                if (ReferenceEquals(manage1, manage2))
                {
                    Console.WriteLine("✓ ComfyUIManage单例模式正常");
                }
                else
                {
                    Console.WriteLine("✗ ComfyUIManage单例模式异常");
                }

                Console.WriteLine("测试ComfyUIData单例模式...");
                var data1 = ComfyUIData.Instance;
                var data2 = ComfyUIData.Instance;
                
                if (ReferenceEquals(data1, data2))
                {
                    Console.WriteLine("✓ ComfyUIData单例模式正常");
                }
                else
                {
                    Console.WriteLine("✗ ComfyUIData单例模式异常");
                }

                Console.WriteLine("✓ ComfyUI管理类基本功能测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试ComfyUI管理类时发生错误: {ex.Message}");
            }
        }
    }
}
