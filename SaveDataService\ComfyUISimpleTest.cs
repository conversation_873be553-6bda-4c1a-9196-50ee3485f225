using System;
using System.IO;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;
using SaveDataService.Manage;

namespace SaveDataService
{
    /// <summary>
    /// ComfyUI简单测试类 - 不依赖数据库
    /// </summary>
    public static class ComfyUISimpleTest
    {
        private static readonly HttpClient _httpClient = new HttpClient();

        /// <summary>
        /// 运行简单测试
        /// </summary>
        public static async Task RunSimpleTests()
        {
            Console.WriteLine("=== ComfyUI简单测试开始 ===");

            try
            {
                // 1. 测试ComfyUI服务器连接
                Console.WriteLine("\n1. 测试ComfyUI服务器连接...");
                await TestComfyUIServerConnection();

                // 2. 测试加载工作流文件
                Console.WriteLine("\n2. 测试加载工作流文件...");
                var workflowJson = LoadWorkflowFile();
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    Console.WriteLine($"工作流加载成功，长度: {workflowJson.Length} 字符");
                }

                // 3. 测试解析工作流
                Console.WriteLine("\n3. 测试解析工作流...");
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    TestParseWorkflow(workflowJson);
                }

                // 4. 测试向ComfyUI服务器提交工作流
                Console.WriteLine("\n4. 测试向ComfyUI服务器提交工作流...");
                if (!string.IsNullOrEmpty(workflowJson))
                {
                    await TestSubmitWorkflow(workflowJson);
                }

                // 5. 测试ComfyUI管理类的基本功能
                Console.WriteLine("\n5. 测试ComfyUI管理类基本功能...");
                TestComfyUIManageBasics();

                Console.WriteLine("\n=== ComfyUI简单测试完成 ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 测试ComfyUI服务器连接
        /// </summary>
        private static async Task TestComfyUIServerConnection()
        {
            try
            {
                var url = "http://127.0.0.1:8888/";
                Console.WriteLine($"尝试连接到: {url}");

                var response = await _httpClient.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✓ ComfyUI服务器连接成功");
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"响应长度: {content.Length} 字符");
                }
                else
                {
                    Console.WriteLine($"✗ ComfyUI服务器连接失败: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 连接ComfyUI服务器时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载工作流文件
        /// </summary>
        private static string LoadWorkflowFile()
        {
            try
            {
                var workflowPath = Path.Combine("Res", "baseExcel", "comfyui", "comfyui-cafelabs-test.json");
                if (File.Exists(workflowPath))
                {
                    var workflowJson = File.ReadAllText(workflowPath);
                    Console.WriteLine($"✓ 成功加载工作流文件: {workflowPath}");
                    return workflowJson;
                }
                else
                {
                    Console.WriteLine($"✗ 工作流文件不存在: {workflowPath}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 加载工作流文件失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 测试解析工作流
        /// </summary>
        private static void TestParseWorkflow(string workflowJson)
        {
            try
            {
                // 尝试解析JSON
                var workflowObj = JsonConvert.DeserializeObject(workflowJson);
                Console.WriteLine("✓ 工作流JSON解析成功");

                // 尝试使用ComfyUIData解析
                var workflowInfo = ComfyUIData.Instance.ParseWorkflow(workflowJson);
                Console.WriteLine($"✓ ComfyUIData解析成功，包含 {workflowInfo.Nodes.Count} 个节点");

                // 显示前几个节点信息
                for (int i = 0; i < Math.Min(3, workflowInfo.Nodes.Count); i++)
                {
                    var node = workflowInfo.Nodes[i];
                    Console.WriteLine($"  节点 {i + 1}: {node.NodeTitle} ({node.NodeType})");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 解析工作流失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试向ComfyUI服务器提交工作流
        /// </summary>
        private static async Task TestSubmitWorkflow(string workflowJson)
        {
            try
            {
                var url = "http://127.0.0.1:8888/prompt";
                
                var requestData = new
                {
                    prompt = JsonConvert.DeserializeObject(workflowJson),
                    client_id = Guid.NewGuid().ToString()
                };

                var json = JsonConvert.SerializeObject(requestData);
                var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");

                Console.WriteLine("向ComfyUI服务器提交工作流...");
                
                var response = await _httpClient.PostAsync(url, content);
                var responseText = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✓ 工作流提交成功");
                    Console.WriteLine($"响应: {responseText}");
                }
                else
                {
                    Console.WriteLine($"✗ 工作流提交失败: {response.StatusCode}");
                    Console.WriteLine($"错误响应: {responseText}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 提交工作流时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 测试ComfyUI管理类基本功能
        /// </summary>
        private static void TestComfyUIManageBasics()
        {
            try
            {
                Console.WriteLine("测试ComfyUIManage单例模式...");
                var manage1 = ComfyUIManage.Instance;
                var manage2 = ComfyUIManage.Instance;
                
                if (ReferenceEquals(manage1, manage2))
                {
                    Console.WriteLine("✓ ComfyUIManage单例模式正常");
                }
                else
                {
                    Console.WriteLine("✗ ComfyUIManage单例模式异常");
                }

                Console.WriteLine("测试ComfyUIData单例模式...");
                var data1 = ComfyUIData.Instance;
                var data2 = ComfyUIData.Instance;
                
                if (ReferenceEquals(data1, data2))
                {
                    Console.WriteLine("✓ ComfyUIData单例模式正常");
                }
                else
                {
                    Console.WriteLine("✗ ComfyUIData单例模式异常");
                }

                Console.WriteLine("✓ ComfyUI管理类基本功能测试完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 测试ComfyUI管理类时发生错误: {ex.Message}");
            }
        }
    }
}
